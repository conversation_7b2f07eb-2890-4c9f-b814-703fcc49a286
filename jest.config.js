module.exports = {
  preset: 'ts-jest/presets/js-with-ts',
  rootDir: '.',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/apps/customer-app/$1',
    '^@indie/constants$': '<rootDir>/packages/constants/src',
    '^tailwind.config$': '<rootDir>/apps/customer-app/tailwind.config.js',
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  testMatch: [
    '<rootDir>/apps/customer-app/**/__tests__/**/*.[jt]s?(x)',
    '<rootDir>/apps/customer-app/**/*.(spec|test).[jt]s?(x)',
    '<rootDir>/packages/**/__tests__/**/*.[jt]s?(x)',
    '<rootDir>/packages/**/*.(spec|test).[jt]s?(x)',
  ],
  setupFilesAfterEnv: [],
  globals: {
    'ts-jest': {
      tsconfig: 'apps/customer-app/tsconfig.json',
    },
  },
};
