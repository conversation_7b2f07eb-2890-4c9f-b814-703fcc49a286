import React from 'react';
import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  FormControlHelper,
  FormControlHelperText,
} from '@/components/ui/form-control';
import { Input, InputField } from '@/components/ui/input';

interface FormFieldProps {
  label: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
  helper?: string;
  type?: 'text' | 'password' | 'email';
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  autoComplete?: string;
  secureTextEntry?: boolean;
  required?: boolean;
  disabled?: boolean;
}

export function FormField({
  label,
  placeholder,
  value,
  onChangeText,
  error,
  helper,
  type = 'text',
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  autoComplete,
  secureTextEntry,
  required = false,
  disabled = false,
}: FormFieldProps) {
  const getKeyboardType = () => {
    if (keyboardType !== 'default') return keyboardType;
    if (type === 'email') return 'email-address';
    return 'default';
  };

  const getAutoCapitalize = () => {
    if (type === 'email') return 'none';
    return autoCapitalize;
  };

  const getSecureTextEntry = () => {
    if (secureTextEntry !== undefined) return secureTextEntry;
    return type === 'password';
  };

  return (
    <FormControl
      isInvalid={!!error}
      isDisabled={disabled}
      isRequired={required}
    >
      <FormControlLabel>
        <FormControlLabelText>{label}</FormControlLabelText>
      </FormControlLabel>
      <Input>
        <InputField
          type={type}
          placeholder={placeholder}
          value={value}
          onChangeText={onChangeText}
          keyboardType={getKeyboardType()}
          autoCapitalize={getAutoCapitalize()}
          autoComplete={autoComplete}
          secureTextEntry={getSecureTextEntry()}
          editable={!disabled}
        />
      </Input>
      {error && (
        <FormControlError>
          <FormControlErrorText>{error}</FormControlErrorText>
        </FormControlError>
      )}
      {helper && !error && (
        <FormControlHelper>
          <FormControlHelperText>{helper}</FormControlHelperText>
        </FormControlHelper>
      )}
    </FormControl>
  );
}
