import React, { useState, useEffect } from 'react';
import { ScrollView } from 'react-native';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Heading } from '@/components/ui/heading';
import { Text } from '@/components/ui/text';
import { Spinner } from '@/components/ui/spinner';
import { Alert, AlertText } from '@/components/ui/alert';
import { Button, ButtonText } from '@/components/ui/button';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import {
  CameraView,
  CameraType,
  useCameraPermissions,
  BarcodeScanningResult,
} from 'expo-camera';
import { useAuth } from '@/contexts/AuthContext';
import { TransactionsService } from '@/services';
import { GradientBar } from '@/components/auth/GradientBar';

// Types for QR code data
interface QRCodeData {
  businessId: string;
  businessName: string;
  qrToken: string;
}

export default function Scan() {
  const { user } = useAuth();
  const [facing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [isProcessing, setIsProcessing] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [scannedData, setScannedData] = useState<QRCodeData | null>(null);
  const [scanCompleted, setScanCompleted] = useState(false);

  useEffect(() => {
    if (permission && !permission.granted) {
      requestPermission();
    }
  }, [permission, requestPermission]);

  // Parse QR code data
  const parseQRCodeData = (data: string): QRCodeData | null => {
    try {
      const parsed = JSON.parse(data);
      if (parsed.businessId && parsed.businessName && parsed.token) {
        return {
          businessId: parsed.businessId,
          businessName: parsed.businessName,
          qrToken: parsed.token,
        };
      }
      return null;
    } catch (error) {
      console.error('Error parsing QR code data:', error);
      return null;
    }
  };

  // Handle QR code scan
  const handleBarCodeScanned = async (result: BarcodeScanningResult) => {
    if (isProcessing || !user?.id || scanCompleted) return;

    const qrData = parseQRCodeData(result.data);
    if (!qrData) {
      setErrorMessage(
        'Invalid QR code format. Please scan a valid business QR code.'
      );
      setScanCompleted(true);
      return;
    }

    setIsProcessing(true);
    setErrorMessage(null);
    setSuccessMessage(null);
    setScannedData(qrData);

    try {
      const response = await TransactionsService.createVisitTransaction(
        user.id,
        qrData.businessId,
        qrData.businessName,
        qrData.qrToken
      );

      if (response.error) {
        setErrorMessage(response.error);
      } else if (response.data) {
        if (response.data.alreadyExists) {
          setSuccessMessage(`You've already visited ${qrData.businessName}!`);
        } else {
          setSuccessMessage(
            `Success! You earned 1 point at ${qrData.businessName}!`
          );
        }
      }
    } catch (error) {
      console.error('Error processing visit transaction:', error);
      setErrorMessage('Failed to process visit. Please try again.');
    } finally {
      setIsProcessing(false);
      setScanCompleted(true);
    }
  };

  // Handle scan again
  const handleScanAgain = () => {
    setScanCompleted(false);
    setIsProcessing(false);
    setSuccessMessage(null);
    setErrorMessage(null);
    setScannedData(null);
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            Scan
          </Heading>

          {/* Colored divider line */}
          <GradientBar />
        </VStack>

        <Box className='px-6 pb-8'>
          <VStack space='xl'>
            {/* Feedback Messages */}
            {successMessage && (
              <Alert action='success' variant='solid'>
                <AlertText>{successMessage}</AlertText>
              </Alert>
            )}
            {errorMessage && (
              <Alert action='error' variant='solid'>
                <AlertText>{errorMessage}</AlertText>
              </Alert>
            )}

            {/* Camera section */}
            <VStack space='lg' className='items-center'>
              <Heading size='xl' className='text-typography-900 font-semibold'>
                Scan a business QR code
              </Heading>
              <Box className='w-full aspect-square bg-black rounded-2xl border-4 border-typography-900 items-center justify-center shadow-lg overflow-hidden'>
                {!permission ? (
                  <Text size='md' className='text-white'>
                    Requesting camera permission...
                  </Text>
                ) : !permission.granted ? (
                  <VStack space='md' className='items-center'>
                    <FontAwesome name='camera' size={64} color='#fff' />
                    <Text size='md' className='text-white text-center'>
                      Camera access is required to scan business QR codes.
                    </Text>
                  </VStack>
                ) : isProcessing ? (
                  <VStack space='md' className='items-center'>
                    <Spinner size='large' color='#fff' />
                    <Text size='md' className='text-white text-center'>
                      Processing visit...
                    </Text>
                    {scannedData && (
                      <Text size='sm' className='text-white text-center'>
                        {scannedData.businessName}
                      </Text>
                    )}
                  </VStack>
                ) : scanCompleted ? (
                  <VStack
                    space='lg'
                    className='items-center justify-center flex-1'
                  >
                    <FontAwesome
                      name='check-circle'
                      size={64}
                      color='#22c55e'
                    />
                    <Text
                      size='lg'
                      className='text-white text-center font-semibold'
                    >
                      Scan complete!
                    </Text>
                    <Button
                      size='lg'
                      action='primary'
                      className='mt-4'
                      onPress={handleScanAgain}
                    >
                      <ButtonText>Scan again</ButtonText>
                    </Button>
                  </VStack>
                ) : (
                  <CameraView
                    style={{ width: '100%', aspectRatio: 1 }}
                    facing={facing}
                    ratio='1:1'
                    barcodeScannerSettings={{
                      barcodeTypes: ['qr'],
                    }}
                    onBarcodeScanned={handleBarCodeScanned}
                  />
                )}
              </Box>
            </VStack>

            {/* How to Claim a Bonus Point Section */}
            <VStack space='lg'>
              <Heading size='xl' className='text-typography-900 font-semibold'>
                How to claim a bonus point
              </Heading>

              {/* Step 1 */}
              <HStack space='md' className='items-start'>
                <Box className='w-8 h-8 bg-primary-500 rounded-full items-center justify-center border-2 border-black'>
                  <Text size='md' className='text-white font-bold'>
                    1
                  </Text>
                </Box>
                <VStack className='flex-1'>
                  <Text size='md' className='text-typography-900 font-semibold'>
                    Visit a participating business
                  </Text>
                  <Text size='sm' className='text-typography-600'>
                    Look for the Indie Points logo at local businesses
                  </Text>
                </VStack>
              </HStack>

              {/* Step 2 */}
              <HStack space='md' className='items-start'>
                <Box className='w-8 h-8 bg-secondary-500 rounded-full items-center justify-center border-2 border-black'>
                  <Text size='md' className='text-white font-bold'>
                    2
                  </Text>
                </Box>
                <VStack className='flex-1'>
                  <Text size='md' className='text-typography-900 font-semibold'>
                    Open the scan tab
                  </Text>
                  <Text size='sm' className='text-typography-600'>
                    Use your phone to scan the QR code of the business
                  </Text>
                </VStack>
              </HStack>

              {/* Step 3 */}
              <HStack space='md' className='items-start'>
                <Box className='w-8 h-8 bg-error-500 rounded-full items-center justify-center border-2 border-black'>
                  <Text size='md' className='text-white font-bold'>
                    3
                  </Text>
                </Box>
                <VStack className='flex-1'>
                  <Text size='md' className='text-typography-900 font-semibold'>
                    Claim your bonus point
                  </Text>
                  <Text size='sm' className='text-typography-600'>
                    After scanning, you will receive a bonus point for visiting
                  </Text>
                </VStack>
              </HStack>
            </VStack>
          </VStack>
        </Box>
      </ScrollView>
    </Box>
  );
}
