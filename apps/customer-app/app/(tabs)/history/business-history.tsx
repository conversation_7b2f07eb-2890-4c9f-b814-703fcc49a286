import React, { useState, useEffect, useCallback } from 'react';
import { ScrollView, ActivityIndicator } from 'react-native';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Heading } from '@/components/ui/heading';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useAuth } from '@/contexts/AuthContext';
import { TransactionsService, CustomerTransaction } from '@/services';
import dayjs from 'dayjs';
import { GradientBar } from '@/components/auth/GradientBar';
import { RefreshControl } from '@/components/ui/refresh-control';
import { useLocalSearchParams, useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';

export default function BusinessHistory() {
  const { user } = useAuth();
  const router = useRouter();
  const { businessId, businessName, businessCategory } = useLocalSearchParams<{
    businessId: string;
    businessName: string;
    businessCategory: string;
  }>();

  const [transactionData, setTransactionData] = useState<CustomerTransaction[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchData = useCallback(async () => {
    if (!user?.id || !businessId) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result =
        await TransactionsService.getCustomerTransactionHistoryForBusiness(
          user.id,
          businessId
        );

      if (result.error) {
        setError(result.error);
      } else {
        setTransactionData(result.data || []);
      }
    } catch (err) {
      setError('An unexpected error occurred while fetching data');
      console.error('Error fetching business history data:', err);
    }

    setLoading(false);
  }, [user?.id, businessId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      fetchData(),
      new Promise(resolve => setTimeout(resolve, 750)),
    ]);
    setRefreshing(false);
  };

  const handleBackPress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.back();
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Back Button and Title */}
          <HStack space='md' className='items-center'>
            <Pressable
              className='w-10 h-10 bg-primary-500 rounded-xl items-center justify-center'
              onPress={handleBackPress}
            >
              <FontAwesome name='arrow-left' size={16} color='white' />
            </Pressable>
            <VStack className='flex-1'>
              <Heading size='3xl' className='text-typography-900 font-bold'>
                {businessName || 'Business History'}
              </Heading>
              {businessCategory && (
                <Text size='md' className='text-typography-600'>
                  {businessCategory}
                </Text>
              )}
            </VStack>
          </HStack>

          {/* Colored divider line */}
          <GradientBar />
        </VStack>

        <Box className='px-6 pb-8'>
          {loading && !refreshing ? (
            <Box className='flex-1 items-center justify-center py-12'>
              <ActivityIndicator size='large' color='#3B82F6' />
              <Text size='md' className='text-typography-600 mt-4'>
                Loading transaction history...
              </Text>
            </Box>
          ) : error ? (
            <Box className='bg-error-50 border-2 border-error-500 rounded-2xl p-4'>
              <HStack space='md' className='items-center'>
                <FontAwesome
                  name='exclamation-triangle'
                  size={20}
                  color='#EF4444'
                />
                <VStack className='flex-1'>
                  <Text size='md' className='text-error-700 font-semibold'>
                    Error loading data
                  </Text>
                  <Text size='sm' className='text-error-600'>
                    {error}
                  </Text>
                </VStack>
              </HStack>
            </Box>
          ) : (
            <VStack space='md'>
              {transactionData.length === 0 ? (
                <Box className='bg-background-50 border-2 border-background-300 rounded-2xl p-6 items-center'>
                  <FontAwesome name='history' size={40} color='#9CA3AF' />
                  <Text
                    size='md'
                    className='text-typography-600 mt-4 text-center'
                  >
                    No transactions found
                  </Text>
                  <Text
                    size='sm'
                    className='text-typography-500 mt-2 text-center'
                  >
                    Your transaction history with this business will appear here
                  </Text>
                </Box>
              ) : (
                transactionData.map(item => (
                  <Box
                    key={item.id}
                    className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-4'
                  >
                    <HStack space='md' className='items-center'>
                      {/* Icon */}
                      <Box
                        className={`w-12 h-12 rounded-xl items-center justify-center ${
                          item.type === 'purchase'
                            ? 'bg-primary-500'
                            : 'bg-error-500'
                        }`}
                      >
                        <FontAwesome
                          name={
                            item.type === 'purchase' ? 'shopping-bag' : 'gift'
                          }
                          size={20}
                          color='white'
                        />
                      </Box>

                      {/* Content */}
                      <VStack className='flex-1'>
                        <Text
                          size='md'
                          className='text-typography-900 font-semibold'
                        >
                          {item.type === 'purchase' ? 'Purchase' : 'Redemption'}
                        </Text>
                        <Text size='sm' className='text-typography-600'>
                          {item.businessCategory}
                        </Text>
                        <Text size='xs' className='text-typography-500'>
                          {dayjs(item.date).format('DD MMM YYYY, HH:mm')}
                        </Text>
                      </VStack>

                      {/* Points */}
                      <Text
                        size='lg'
                        className={`font-bold ${
                          item.type === 'redemption'
                            ? 'text-error-500'
                            : 'text-primary-500'
                        }`}
                      >
                        {item.type === 'redemption'
                          ? `-${item.pointsRedeemed ?? 0} pts`
                          : `${item.pointsEarned > 0 ? '+' : ''}${item.pointsEarned} pts`}
                      </Text>
                    </HStack>
                  </Box>
                ))
              )}
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
