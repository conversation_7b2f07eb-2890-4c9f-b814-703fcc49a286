import React, { useState, useCallback } from 'react';
import { ScrollView } from 'react-native';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Heading } from '@/components/ui/heading';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useAuth } from '@/contexts/AuthContext';
import QRCode from 'react-native-qrcode-svg';
import { GradientBar } from '@/components/auth/GradientBar';
import * as Haptics from 'expo-haptics';

export default function Points() {
  const { user } = useAuth();

  // Helper to generate QR code payload
  const generateQrPayload = useCallback(() => {
    const now = Date.now();
    return JSON.stringify({
      expiry: now + 5 * 60 * 1000,
      issuedAt: now,
      userId: user?.id ?? null,
    });
  }, [user?.id]);

  const [qrValue, setQrValue] = useState(generateQrPayload);
  const [qrWidth, setQrWidth] = useState(0);

  const handleGenerateNewQr = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setQrValue(generateQrPayload());
  }, [generateQrPayload]);

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            Points
          </Heading>

          {/* Colored divider line */}
          <GradientBar />
        </VStack>

        <Box className='px-6 pb-8'>
          <VStack space='xl'>
            {/* Your loyalty card section */}
            <VStack space='lg' className='items-center'>
              <Heading size='xl' className='text-typography-900 font-semibold'>
                Your loyalty card
              </Heading>

              {/* QR Code */}
              <Box
                className='w-full max-w-md aspect-square bg-white rounded-2xl border-4 p-4 border-typography-900 items-center justify-center shadow-lg'
                onLayout={event => {
                  const width = event.nativeEvent.layout.width;
                  setQrWidth(width);
                }}
              >
                <VStack space='md' className='items-center w-full h-full'>
                  {user?.id ? (
                    qrWidth > 0 && (
                      <QRCode
                        value={qrValue}
                        size={Math.max(qrWidth - 8 - 32, 0)}
                        logo={require('../../assets/images/icon.png')}
                        logoSize={Math.max((qrWidth - 8 - 32) * 0.2, 32)}
                        logoBackgroundColor='transparent'
                      />
                    )
                  ) : (
                    <FontAwesome name='qrcode' size={120} color='#000' />
                  )}
                </VStack>
              </Box>

              {/* Action Buttons */}
              <VStack space='md' className='w-full'>
                <Button
                  size='lg'
                  className='w-full bg-primary-500 rounded-xl border-2 border-primary-700 shadow-lg'
                  onPress={handleGenerateNewQr}
                >
                  <HStack space='sm' className='items-center'>
                    <FontAwesome name='refresh' size={16} color='white' />
                    <ButtonText className='text-white font-semibold'>
                      Regenerate
                    </ButtonText>
                  </HStack>
                </Button>
              </VStack>
            </VStack>

            {/* How to Earn Points Section */}
            <VStack space='lg'>
              <Heading size='xl' className='text-typography-900 font-semibold'>
                How to earn points
              </Heading>

              {/* Step 1 */}
              <HStack space='md' className='items-start'>
                <Box className='w-8 h-8 bg-primary-500 rounded-full items-center justify-center border-2 border-black'>
                  <Text size='md' className='text-white font-bold'>
                    1
                  </Text>
                </Box>
                <VStack className='flex-1'>
                  <Text size='md' className='text-typography-900 font-semibold'>
                    Visit a participating business
                  </Text>
                  <Text size='sm' className='text-typography-600'>
                    Look for the Indie Points logo at local businesses
                  </Text>
                </VStack>
              </HStack>

              {/* Step 2 */}
              <HStack space='md' className='items-start'>
                <Box className='w-8 h-8 bg-secondary-500 rounded-full items-center justify-center border-2 border-black'>
                  <Text size='md' className='text-white font-bold'>
                    2
                  </Text>
                </Box>
                <VStack className='flex-1'>
                  <Text size='md' className='text-typography-900 font-semibold'>
                    Show your QR code
                  </Text>
                  <Text size='sm' className='text-typography-600'>
                    Let the business scan your unique QR code before or after
                    purchase
                  </Text>
                </VStack>
              </HStack>

              {/* Step 3 */}
              <HStack space='md' className='items-start'>
                <Box className='w-8 h-8 bg-error-500 rounded-full items-center justify-center border-2 border-black'>
                  <Text size='md' className='text-white font-bold'>
                    3
                  </Text>
                </Box>
                <VStack className='flex-1'>
                  <Text size='md' className='text-typography-900 font-semibold'>
                    Earn points automatically
                  </Text>
                  <Text size='sm' className='text-typography-600'>
                    Get 1 point for every £1 spent at participating businesses
                  </Text>
                </VStack>
              </HStack>
            </VStack>
          </VStack>
        </Box>
      </ScrollView>
    </Box>
  );
}
